import { test, expect } from '@playwright/test'

test.describe('FileSearch E2E Tests', () => {
  let subjectId: number

  test.beforeEach(async ({ page }) => {
    // 确保后端服务器运行
    await page.goto('http://localhost:3001/health')
    await expect(page.locator('body')).toContainText('OK')
    
    // 导航到前端应用
    await page.goto('http://localhost:3000')
    await page.waitForLoadState('networkidle')
    
    // 创建测试学科
    await page.goto('http://localhost:3000/subjects')
    await page.click('button:has-text("创建学科")')
    await page.fill('input[placeholder="请输入学科名称"]', '文件搜索测试学科')
    await page.fill('textarea[placeholder="请输入学科描述"]', '用于测试文件搜索功能的学科')
    await page.click('button:has-text("创建")')
    await page.waitForSelector('.ant-message-success', { timeout: 5000 })
    
    // 获取学科ID并进入详情页
    const subjectCard = page.locator('.subject-card:has-text("文件搜索测试学科")')
    await subjectCard.click()
    
    // 从URL中提取学科ID
    await page.waitForURL(/\/subjects\/\d+/)
    const url = page.url()
    subjectId = parseInt(url.match(/\/subjects\/(\d+)/)?.[1] || '0')
    
    // 等待页面加载完成
    await page.waitForSelector('.file-search', { timeout: 10000 })
  })

  test.afterEach(async ({ page }) => {
    // 清理测试数据
    if (subjectId) {
      await page.evaluate(async (id) => {
        await fetch(`http://localhost:3001/api/subjects/${id}`, {
          method: 'DELETE'
        })
      }, subjectId)
    }
  })

  test('should display search interface correctly', async ({ page }) => {
    // 验证搜索界面基本元素
    await expect(page.locator('.file-search')).toBeVisible()
    await expect(page.locator('.search-input')).toBeVisible()
    await expect(page.locator('.search-button')).toBeVisible()
    
    // 验证搜索输入框
    const searchInput = page.locator('input[placeholder*="搜索文件"]')
    await expect(searchInput).toBeVisible()
    await expect(searchInput).toBeEditable()
  })

  test('should perform basic search functionality', async ({ page }) => {
    const searchInput = page.locator('input[placeholder*="搜索文件"]')
    const searchButton = page.locator('.search-button')
    
    // 输入搜索关键词
    await searchInput.fill('测试')
    await searchButton.click()
    
    // 等待搜索结果
    await page.waitForTimeout(1000)
    
    // 验证搜索结果区域显示
    const searchResults = page.locator('.search-results')
    if (await searchResults.isVisible()) {
      await expect(searchResults).toBeVisible()
      
      // 验证搜索结果标题
      const resultsTitle = page.locator('.search-results-title')
      if (await resultsTitle.isVisible()) {
        await expect(resultsTitle).toContainText('搜索结果')
      }
    } else {
      // 如果没有结果，验证空状态
      const noResults = page.locator('.no-search-results')
      if (await noResults.isVisible()) {
        await expect(noResults).toContainText('未找到相关文件')
      }
    }
  })

  test('should handle real-time search suggestions', async ({ page }) => {
    const searchInput = page.locator('input[placeholder*="搜索文件"]')
    
    // 输入部分关键词
    await searchInput.fill('测')
    await page.waitForTimeout(500)
    
    // 验证搜索建议
    const suggestions = page.locator('.search-suggestions')
    if (await suggestions.isVisible()) {
      await expect(suggestions).toBeVisible()
      
      // 点击第一个建议
      const firstSuggestion = suggestions.locator('.suggestion-item').first()
      if (await firstSuggestion.isVisible()) {
        await firstSuggestion.click()
        
        // 验证搜索输入框更新
        const inputValue = await searchInput.inputValue()
        expect(inputValue.length).toBeGreaterThan(1)
      }
    }
  })

  test('should handle search history functionality', async ({ page }) => {
    const searchInput = page.locator('input[placeholder*="搜索文件"]')
    const searchButton = page.locator('.search-button')
    
    // 执行几次搜索
    const searchTerms = ['测试', '文档', '资料']
    
    for (const term of searchTerms) {
      await searchInput.fill(term)
      await searchButton.click()
      await page.waitForTimeout(500)
      await searchInput.clear()
    }
    
    // 点击搜索历史按钮
    const historyButton = page.locator('.search-history-button')
    if (await historyButton.isVisible()) {
      await historyButton.click()
      
      // 验证搜索历史显示
      const historyPanel = page.locator('.search-history')
      if (await historyPanel.isVisible()) {
        await expect(historyPanel).toBeVisible()
        
        // 验证历史记录项
        const historyItems = historyPanel.locator('.history-item')
        const count = await historyItems.count()
        expect(count).toBeGreaterThan(0)
        
        // 点击第一个历史记录
        if (count > 0) {
          await historyItems.first().click()
          
          // 验证搜索输入框更新
          const inputValue = await searchInput.inputValue()
          expect(searchTerms).toContain(inputValue)
        }
      }
    }
  })

  test('should handle search result highlighting', async ({ page }) => {
    const searchInput = page.locator('input[placeholder*="搜索文件"]')
    const searchButton = page.locator('.search-button')
    
    // 搜索特定关键词
    await searchInput.fill('测试')
    await searchButton.click()
    await page.waitForTimeout(1000)
    
    // 验证搜索结果中的高亮显示
    const searchResults = page.locator('.search-results')
    if (await searchResults.isVisible()) {
      const highlightedText = searchResults.locator('.highlight')
      const count = await highlightedText.count()
      
      if (count > 0) {
        // 验证高亮文本包含搜索关键词
        for (let i = 0; i < Math.min(count, 3); i++) {
          const text = await highlightedText.nth(i).textContent()
          expect(text?.toLowerCase()).toContain('测试')
        }
      }
    }
  })

  test('should handle advanced search filters', async ({ page }) => {
    // 打开高级搜索
    const advancedButton = page.locator('.advanced-search-button')
    if (await advancedButton.isVisible()) {
      await advancedButton.click()
      
      // 验证高级搜索面板
      const advancedPanel = page.locator('.advanced-search-panel')
      if (await advancedPanel.isVisible()) {
        await expect(advancedPanel).toBeVisible()
        
        // 测试文件类型过滤
        const fileTypeFilter = advancedPanel.locator('.file-type-filter')
        if (await fileTypeFilter.isVisible()) {
          await fileTypeFilter.selectOption('md')
          
          // 执行搜索
          const searchInput = page.locator('input[placeholder*="搜索文件"]')
          await searchInput.fill('文档')
          await page.locator('.search-button').click()
          await page.waitForTimeout(1000)
          
          // 验证结果只包含Markdown文件
          const results = page.locator('.search-result-item')
          const count = await results.count()
          
          if (count > 0) {
            for (let i = 0; i < Math.min(count, 3); i++) {
              const fileName = await results.nth(i).locator('.file-name').textContent()
              expect(fileName).toMatch(/\.md$/)
            }
          }
        }
        
        // 测试日期范围过滤
        const dateFilter = advancedPanel.locator('.date-range-filter')
        if (await dateFilter.isVisible()) {
          // 设置日期范围（这里需要根据实际的日期选择器实现）
          const startDate = dateFilter.locator('.start-date')
          const endDate = dateFilter.locator('.end-date')
          
          if (await startDate.isVisible() && await endDate.isVisible()) {
            await startDate.fill('2025-01-01')
            await endDate.fill('2025-12-31')
          }
        }
      }
    }
  })

  test('should handle search performance with large datasets', async ({ page }) => {
    // 测试搜索性能
    const searchInput = page.locator('input[placeholder*="搜索文件"]')
    const searchButton = page.locator('.search-button')
    
    // 记录搜索开始时间
    const startTime = Date.now()
    
    // 执行搜索
    await searchInput.fill('测试')
    await searchButton.click()
    
    // 等待搜索结果
    await page.waitForSelector('.search-results, .no-search-results', { timeout: 5000 })
    
    // 计算搜索耗时
    const endTime = Date.now()
    const searchTime = endTime - startTime
    
    // 验证搜索响应时间 < 2秒
    expect(searchTime).toBeLessThan(2000)
    
    // 验证搜索结果加载完成
    const results = page.locator('.search-results')
    if (await results.isVisible()) {
      await expect(results).toBeVisible()
    }
  })

  test('should handle search error states', async ({ page }) => {
    // 模拟搜索API错误
    await page.route('**/api/subjects/*/search*', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          message: '搜索服务暂时不可用'
        })
      })
    })
    
    const searchInput = page.locator('input[placeholder*="搜索文件"]')
    const searchButton = page.locator('.search-button')
    
    // 执行搜索
    await searchInput.fill('测试')
    await searchButton.click()
    await page.waitForTimeout(1000)
    
    // 验证错误状态显示
    const errorMessage = page.locator('.search-error')
    if (await errorMessage.isVisible()) {
      await expect(errorMessage).toBeVisible()
      await expect(errorMessage).toContainText('搜索失败')
    }
    
    // 清除路由拦截
    await page.unroute('**/api/subjects/*/search*')
  })

  test('should handle empty search queries', async ({ page }) => {
    const searchInput = page.locator('input[placeholder*="搜索文件"]')
    const searchButton = page.locator('.search-button')
    
    // 尝试搜索空字符串
    await searchInput.fill('')
    await searchButton.click()
    
    // 验证不执行搜索或显示提示
    const emptyQueryMessage = page.locator('.empty-query-message')
    if (await emptyQueryMessage.isVisible()) {
      await expect(emptyQueryMessage).toContainText('请输入搜索关键词')
    }
    
    // 尝试搜索只有空格的字符串
    await searchInput.fill('   ')
    await searchButton.click()
    
    // 验证处理空白字符
    if (await emptyQueryMessage.isVisible()) {
      await expect(emptyQueryMessage).toBeVisible()
    }
  })

  test('should handle keyboard shortcuts', async ({ page }) => {
    const searchInput = page.locator('input[placeholder*="搜索文件"]')
    
    // 测试Ctrl+F快捷键聚焦搜索框
    await page.keyboard.press('Control+f')
    await expect(searchInput).toBeFocused()
    
    // 测试Enter键执行搜索
    await searchInput.fill('测试')
    await page.keyboard.press('Enter')
    await page.waitForTimeout(1000)
    
    // 验证搜索执行
    const results = page.locator('.search-results, .no-search-results')
    await expect(results).toBeVisible()
    
    // 测试Escape键清空搜索
    await page.keyboard.press('Escape')
    const inputValue = await searchInput.inputValue()
    expect(inputValue).toBe('')
  })
})
