import { test, expect } from '@playwright/test'

test.describe('FileBrowser E2E Tests', () => {
  let subjectId: number

  test.beforeEach(async ({ page }) => {
    // 确保后端服务器运行
    await page.goto('http://localhost:3001/health')
    await expect(page.locator('body')).toContainText('OK')

    // 导航到前端应用
    await page.goto('http://localhost:3000')
    await page.waitForLoadState('networkidle')

    // 创建测试学科
    await page.goto('http://localhost:3000/subjects')
    await page.click('button:has-text("创建学科")')
    await page.fill('input[placeholder="请输入学科名称"]', '文件浏览测试学科')
    await page.fill('textarea[placeholder="请输入学科描述"]', '用于测试文件浏览功能的学科')
    await page.click('button:has-text("创建")')
    await page.waitForSelector('.ant-message-success', { timeout: 5000 })

    // 获取学科ID并进入详情页
    const subjectCard = page.locator('.subject-card:has-text("文件浏览测试学科")')
    await subjectCard.click()

    // 从URL中提取学科ID
    await page.waitForURL(/\/subjects\/\d+/)
    const url = page.url()
    subjectId = parseInt(url.match(/\/subjects\/(\d+)/)?.[1] || '0')

    // 等待页面加载完成
    await page.waitForSelector('.file-browser', { timeout: 10000 })
  })

  test.afterEach(async ({ page }) => {
    // 清理测试数据 - 删除创建的学科
    if (subjectId) {
      await page.evaluate(async (id) => {
        await fetch(`http://localhost:3001/api/subjects/${id}`, {
          method: 'DELETE'
        })
      }, subjectId)
    }
  })

  test('should display file browser interface correctly', async ({ page }) => {
    // 验证文件浏览器基本界面元素
    await expect(page.locator('.file-browser')).toBeVisible()
    await expect(page.locator('.file-browser-header')).toBeVisible()
    await expect(page.locator('.file-browser-toolbar')).toBeVisible()
    await expect(page.locator('.file-browser-content')).toBeVisible()

    // 验证工具栏元素
    await expect(page.locator('input[placeholder*="搜索文件"]')).toBeVisible()
    await expect(page.locator('.view-toggle')).toBeVisible()
    await expect(page.locator('.type-filter')).toBeVisible()

    // 验证初始状态显示
    const fileCount = page.locator('.file-count')
    await expect(fileCount).toBeVisible()
  })

  test('should handle empty folder state correctly', async ({ page }) => {
    // 验证空文件夹状态
    const emptyState = page.locator('.empty-folder')
    if (await emptyState.isVisible()) {
      await expect(emptyState).toContainText('文件夹为空')
      await expect(page.locator('.empty-folder-description')).toContainText('当前目录下没有文件或文件夹')
    }
  })

  test('should switch between list and grid view modes', async ({ page }) => {
    // 测试视图切换功能
    const listViewBtn = page.locator('.view-toggle .list-view')
    const gridViewBtn = page.locator('.view-toggle .grid-view')

    // 切换到网格视图
    await gridViewBtn.click()
    await expect(page.locator('.file-grid')).toBeVisible()

    // 切换回列表视图
    await listViewBtn.click()
    await expect(page.locator('.file-list')).toBeVisible()
  })

  test('should filter files by type correctly', async ({ page }) => {
    // 测试文件类型过滤
    const typeFilter = page.locator('.type-filter select')

    // 选择只显示文件
    await typeFilter.selectOption('file')
    await page.waitForTimeout(500) // 等待过滤生效

    // 验证只显示文件类型
    const fileItems = page.locator('.file-item')
    const count = await fileItems.count()

    if (count > 0) {
      for (let i = 0; i < count; i++) {
        const item = fileItems.nth(i)
        await expect(item).not.toHaveClass(/folder/)
      }
    }

    // 选择只显示文件夹
    await typeFilter.selectOption('folder')
    await page.waitForTimeout(500)

    // 重置过滤器
    await typeFilter.selectOption('')
  })

  test('should handle file search functionality', async ({ page }) => {
    // 测试文件搜索功能
    const searchInput = page.locator('input[placeholder*="搜索文件"]')
    const searchBtn = page.locator('.search-button')

    // 输入搜索关键词
    await searchInput.fill('测试')
    await searchBtn.click()

    // 等待搜索结果
    await page.waitForTimeout(1000)

    // 验证搜索结果
    const searchResults = page.locator('.search-results')
    if (await searchResults.isVisible()) {
      await expect(searchResults).toBeVisible()
    }

    // 清空搜索
    await searchInput.clear()
    await page.keyboard.press('Enter')
  })

  test('should navigate through folder hierarchy', async ({ page }) => {
    // 测试文件夹导航功能
    const folderItems = page.locator('.file-item.folder')
    const folderCount = await folderItems.count()

    if (folderCount > 0) {
      // 双击进入第一个文件夹
      const firstFolder = folderItems.first()
      const folderName = await firstFolder.locator('.file-name').textContent()

      await firstFolder.dblclick()
      await page.waitForTimeout(1000)

      // 验证面包屑导航更新
      const breadcrumb = page.locator('.breadcrumb-nav')
      if (await breadcrumb.isVisible()) {
        await expect(breadcrumb).toContainText(folderName || '')
      }

      // 返回上级目录
      const backBtn = page.locator('.back-button')
      if (await backBtn.isVisible()) {
        await backBtn.click()
        await page.waitForTimeout(500)
      }
    }
  })

  test('should handle responsive layout on different screen sizes', async ({ page }) => {
    // 测试响应式布局

    // 桌面视图 (1280x720)
    await page.setViewportSize({ width: 1280, height: 720 })
    await expect(page.locator('.file-browser')).toBeVisible()

    // 平板视图 (768x1024)
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(500)
    await expect(page.locator('.file-browser')).toBeVisible()

    // 手机视图 (375x667)
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(500)
    await expect(page.locator('.file-browser')).toBeVisible()

    // 验证移动端特定元素
    const mobileToolbar = page.locator('.mobile-toolbar')
    if (await mobileToolbar.isVisible()) {
      await expect(mobileToolbar).toBeVisible()
    }

    // 恢复桌面视图
    await page.setViewportSize({ width: 1280, height: 720 })
  })

  test('should handle virtual scrolling with large file lists', async ({ page }) => {
    // 测试虚拟滚动功能
    // 首先检查是否有大量文件
    const fileItems = page.locator('.file-item')
    const count = await fileItems.count()

    if (count > 50) {
      // 测试滚动性能
      const scrollContainer = page.locator('.virtual-scroll-container')
      if (await scrollContainer.isVisible()) {
        // 滚动到底部
        await scrollContainer.evaluate(el => {
          el.scrollTop = el.scrollHeight
        })
        await page.waitForTimeout(500)

        // 验证虚拟滚动正常工作
        await expect(scrollContainer).toBeVisible()

        // 滚动回顶部
        await scrollContainer.evaluate(el => {
          el.scrollTop = 0
        })
        await page.waitForTimeout(500)
      }
    }
  })

  test('should handle error states gracefully', async ({ page }) => {
    // 测试错误处理

    // 模拟网络错误
    await page.route('**/api/subjects/*/files*', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          message: '服务器内部错误'
        })
      })
    })

    // 刷新页面触发错误
    await page.reload()
    await page.waitForTimeout(2000)

    // 验证错误状态显示
    const errorMessage = page.locator('.error-message')
    if (await errorMessage.isVisible()) {
      await expect(errorMessage).toBeVisible()
    }

    // 清除路由拦截
    await page.unroute('**/api/subjects/*/files*')
  })

  test('should handle loading states correctly', async ({ page }) => {
    // 测试加载状态

    // 模拟慢速网络
    await page.route('**/api/subjects/*/files*', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000))
      route.continue()
    })

    // 触发加载
    await page.reload()

    // 验证加载状态
    const loadingSpinner = page.locator('.loading-spinner')
    if (await loadingSpinner.isVisible()) {
      await expect(loadingSpinner).toBeVisible()
    }

    // 等待加载完成
    await page.waitForTimeout(3000)

    // 清除路由拦截
    await page.unroute('**/api/subjects/*/files*')
  })

  test('should handle keyboard navigation', async ({ page }) => {
    // 测试键盘导航
    const fileItems = page.locator('.file-item')
    const count = await fileItems.count()

    if (count > 0) {
      // 聚焦第一个文件项
      await fileItems.first().focus()

      // 使用方向键导航
      await page.keyboard.press('ArrowDown')
      await page.waitForTimeout(200)

      // 使用Enter键选择
      await page.keyboard.press('Enter')
      await page.waitForTimeout(500)

      // 使用Escape键取消
      await page.keyboard.press('Escape')
      await page.waitForTimeout(200)
    }
  })

  test('should handle file selection and batch operations', async ({ page }) => {
    // 测试文件选择和批量操作
    const fileItems = page.locator('.file-item')
    const count = await fileItems.count()

    if (count > 1) {
      // 选择第一个文件
      await fileItems.first().click()
      await expect(fileItems.first()).toHaveClass(/selected/)

      // 使用Ctrl+Click多选
      await page.keyboard.down('Control')
      await fileItems.nth(1).click()
      await page.keyboard.up('Control')

      // 验证多选状态
      await expect(fileItems.first()).toHaveClass(/selected/)
      await expect(fileItems.nth(1)).toHaveClass(/selected/)

      // 全选功能
      const selectAllBtn = page.locator('.select-all-button')
      if (await selectAllBtn.isVisible()) {
        await selectAllBtn.click()

        // 验证所有项目被选中
        const selectedItems = page.locator('.file-item.selected')
        const selectedCount = await selectedItems.count()
        expect(selectedCount).toBe(count)
      }

      // 取消选择
      await page.keyboard.press('Escape')
    }
  })

  test('should handle drag and drop operations', async ({ page }) => {
    // 测试拖拽操作
    const fileItems = page.locator('.file-item')
    const folderItems = page.locator('.file-item.folder')

    const fileCount = await fileItems.count()
    const folderCount = await folderItems.count()

    if (fileCount > 0 && folderCount > 0) {
      const sourceFile = fileItems.first()
      const targetFolder = folderItems.first()

      // 执行拖拽操作
      await sourceFile.dragTo(targetFolder)
      await page.waitForTimeout(1000)

      // 验证拖拽结果（这里可能需要根据实际实现调整）
      // 注意：实际的拖拽功能可能需要后端支持
    }
  })

  test('should handle context menu operations', async ({ page }) => {
    // 测试右键菜单
    const fileItems = page.locator('.file-item')
    const count = await fileItems.count()

    if (count > 0) {
      // 右键点击第一个文件
      await fileItems.first().click({ button: 'right' })
      await page.waitForTimeout(500)

      // 验证上下文菜单显示
      const contextMenu = page.locator('.context-menu')
      if (await contextMenu.isVisible()) {
        await expect(contextMenu).toBeVisible()

        // 验证菜单项
        await expect(contextMenu.locator('.menu-item')).toHaveCount(3) // 假设有3个菜单项

        // 点击其他地方关闭菜单
        await page.click('body')
        await expect(contextMenu).not.toBeVisible()
      }
    }
  })
})
