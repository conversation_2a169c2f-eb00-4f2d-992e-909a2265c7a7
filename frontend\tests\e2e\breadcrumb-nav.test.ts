import { test, expect } from '@playwright/test'

test.describe('BreadcrumbNav E2E Tests', () => {
  let subjectId: number

  test.beforeEach(async ({ page }) => {
    // 确保后端服务器运行
    await page.goto('http://localhost:3001/health')
    await expect(page.locator('body')).toContainText('OK')
    
    // 导航到前端应用
    await page.goto('http://localhost:3000')
    await page.waitForLoadState('networkidle')
    
    // 创建测试学科
    await page.goto('http://localhost:3000/subjects')
    await page.click('button:has-text("创建学科")')
    await page.fill('input[placeholder="请输入学科名称"]', '面包屑导航测试学科')
    await page.fill('textarea[placeholder="请输入学科描述"]', '用于测试面包屑导航功能的学科')
    await page.click('button:has-text("创建")')
    await page.waitForSelector('.ant-message-success', { timeout: 5000 })
    
    // 获取学科ID并进入详情页
    const subjectCard = page.locator('.subject-card:has-text("面包屑导航测试学科")')
    await subjectCard.click()
    
    // 从URL中提取学科ID
    await page.waitForURL(/\/subjects\/\d+/)
    const url = page.url()
    subjectId = parseInt(url.match(/\/subjects\/(\d+)/)?.[1] || '0')
    
    // 等待页面加载完成
    await page.waitForSelector('.breadcrumb-nav', { timeout: 10000 })
  })

  test.afterEach(async ({ page }) => {
    // 清理测试数据
    if (subjectId) {
      await page.evaluate(async (id) => {
        await fetch(`http://localhost:3001/api/subjects/${id}`, {
          method: 'DELETE'
        })
      }, subjectId)
    }
  })

  test('should display breadcrumb navigation correctly', async ({ page }) => {
    // 验证面包屑导航基本元素
    await expect(page.locator('.breadcrumb-nav')).toBeVisible()
    
    // 验证根级面包屑项
    const breadcrumbItems = page.locator('.breadcrumb-item')
    const count = await breadcrumbItems.count()
    expect(count).toBeGreaterThanOrEqual(1)
    
    // 验证第一个项目是学科名称
    const firstItem = breadcrumbItems.first()
    await expect(firstItem).toBeVisible()
    await expect(firstItem).toContainText('面包屑导航测试学科')
  })

  test('should handle breadcrumb navigation clicks', async ({ page }) => {
    // 首先需要导航到子文件夹以创建面包屑路径
    const folderItems = page.locator('.file-item.folder')
    const folderCount = await folderItems.count()
    
    if (folderCount > 0) {
      // 进入第一个文件夹
      const firstFolder = folderItems.first()
      const folderName = await firstFolder.locator('.file-name').textContent()
      
      await firstFolder.dblclick()
      await page.waitForTimeout(1000)
      
      // 验证面包屑更新
      const breadcrumbItems = page.locator('.breadcrumb-item')
      const count = await breadcrumbItems.count()
      expect(count).toBeGreaterThanOrEqual(2)
      
      // 验证包含文件夹名称
      if (folderName) {
        await expect(page.locator('.breadcrumb-nav')).toContainText(folderName)
      }
      
      // 点击面包屑中的学科名称返回根目录
      const subjectBreadcrumb = breadcrumbItems.first()
      await subjectBreadcrumb.click()
      await page.waitForTimeout(500)
      
      // 验证返回到根目录
      const updatedCount = await page.locator('.breadcrumb-item').count()
      expect(updatedCount).toBe(1)
    }
  })

  test('should handle breadcrumb overflow with ellipsis', async ({ page }) => {
    // 测试面包屑溢出处理
    // 这需要创建深层次的文件夹结构
    
    // 设置较小的视口以触发溢出
    await page.setViewportSize({ width: 600, height: 400 })
    
    // 模拟深层次导航
    let currentDepth = 0
    const maxDepth = 5
    
    while (currentDepth < maxDepth) {
      const folderItems = page.locator('.file-item.folder')
      const folderCount = await folderItems.count()
      
      if (folderCount > 0) {
        await folderItems.first().dblclick()
        await page.waitForTimeout(500)
        currentDepth++
      } else {
        break
      }
    }
    
    // 验证面包屑溢出处理
    const breadcrumbNav = page.locator('.breadcrumb-nav')
    const ellipsis = breadcrumbNav.locator('.breadcrumb-ellipsis')
    
    if (await ellipsis.isVisible()) {
      await expect(ellipsis).toBeVisible()
      
      // 点击省略号显示完整路径
      await ellipsis.click()
      await page.waitForTimeout(300)
      
      // 验证完整路径显示
      const expandedNav = page.locator('.breadcrumb-expanded')
      if (await expandedNav.isVisible()) {
        await expect(expandedNav).toBeVisible()
      }
    }
    
    // 恢复视口大小
    await page.setViewportSize({ width: 1280, height: 720 })
  })

  test('should handle breadcrumb hover effects', async ({ page }) => {
    const breadcrumbItems = page.locator('.breadcrumb-item')
    const count = await breadcrumbItems.count()
    
    if (count > 0) {
      const firstItem = breadcrumbItems.first()
      
      // 悬停在面包屑项上
      await firstItem.hover()
      await page.waitForTimeout(200)
      
      // 验证悬停效果
      await expect(firstItem).toHaveClass(/hover/)
      
      // 验证工具提示显示
      const tooltip = page.locator('.breadcrumb-tooltip')
      if (await tooltip.isVisible()) {
        await expect(tooltip).toBeVisible()
        
        // 验证工具提示内容
        const tooltipText = await tooltip.textContent()
        expect(tooltipText).toBeTruthy()
      }
    }
  })

  test('should handle breadcrumb keyboard navigation', async ({ page }) => {
    const breadcrumbItems = page.locator('.breadcrumb-item')
    const count = await breadcrumbItems.count()
    
    if (count > 0) {
      // 聚焦第一个面包屑项
      await breadcrumbItems.first().focus()
      
      // 使用Tab键导航
      await page.keyboard.press('Tab')
      await page.waitForTimeout(200)
      
      // 使用Enter键激活
      await page.keyboard.press('Enter')
      await page.waitForTimeout(500)
      
      // 验证导航效果
      // 这里的具体验证取决于面包屑的实际行为
    }
  })

  test('should handle breadcrumb context menu', async ({ page }) => {
    const breadcrumbItems = page.locator('.breadcrumb-item')
    const count = await breadcrumbItems.count()
    
    if (count > 0) {
      // 右键点击面包屑项
      await breadcrumbItems.first().click({ button: 'right' })
      await page.waitForTimeout(300)
      
      // 验证上下文菜单
      const contextMenu = page.locator('.breadcrumb-context-menu')
      if (await contextMenu.isVisible()) {
        await expect(contextMenu).toBeVisible()
        
        // 验证菜单项
        const menuItems = contextMenu.locator('.menu-item')
        const menuCount = await menuItems.count()
        expect(menuCount).toBeGreaterThan(0)
        
        // 点击其他地方关闭菜单
        await page.click('body')
        await expect(contextMenu).not.toBeVisible()
      }
    }
  })

  test('should handle breadcrumb responsive behavior', async ({ page }) => {
    // 测试响应式行为
    
    // 桌面视图
    await page.setViewportSize({ width: 1280, height: 720 })
    await expect(page.locator('.breadcrumb-nav')).toBeVisible()
    
    // 验证桌面版面包屑样式
    const desktopBreadcrumb = page.locator('.breadcrumb-nav.desktop')
    if (await desktopBreadcrumb.isVisible()) {
      await expect(desktopBreadcrumb).toBeVisible()
    }
    
    // 平板视图
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(300)
    
    // 验证平板版面包屑适配
    const tabletBreadcrumb = page.locator('.breadcrumb-nav.tablet')
    if (await tabletBreadcrumb.isVisible()) {
      await expect(tabletBreadcrumb).toBeVisible()
    }
    
    // 手机视图
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(300)
    
    // 验证移动版面包屑
    const mobileBreadcrumb = page.locator('.breadcrumb-nav.mobile')
    if (await mobileBreadcrumb.isVisible()) {
      await expect(mobileBreadcrumb).toBeVisible()
    } else {
      // 或者验证面包屑在移动端被隐藏或简化
      const simplifiedNav = page.locator('.simplified-nav')
      if (await simplifiedNav.isVisible()) {
        await expect(simplifiedNav).toBeVisible()
      }
    }
    
    // 恢复桌面视图
    await page.setViewportSize({ width: 1280, height: 720 })
  })

  test('should handle breadcrumb loading states', async ({ page }) => {
    // 模拟慢速面包屑加载
    await page.route('**/api/files/*/breadcrumb*', async route => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      route.continue()
    })
    
    // 导航到文件夹触发面包屑加载
    const folderItems = page.locator('.file-item.folder')
    const folderCount = await folderItems.count()
    
    if (folderCount > 0) {
      await folderItems.first().dblclick()
      
      // 验证加载状态
      const loadingIndicator = page.locator('.breadcrumb-loading')
      if (await loadingIndicator.isVisible()) {
        await expect(loadingIndicator).toBeVisible()
      }
      
      // 等待加载完成
      await page.waitForTimeout(1500)
      
      // 验证面包屑更新
      const breadcrumbItems = page.locator('.breadcrumb-item')
      const count = await breadcrumbItems.count()
      expect(count).toBeGreaterThanOrEqual(2)
    }
    
    // 清除路由拦截
    await page.unroute('**/api/files/*/breadcrumb*')
  })

  test('should handle breadcrumb error states', async ({ page }) => {
    // 模拟面包屑API错误
    await page.route('**/api/files/*/breadcrumb*', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          message: '获取路径信息失败'
        })
      })
    })
    
    // 导航到文件夹触发面包屑加载
    const folderItems = page.locator('.file-item.folder')
    const folderCount = await folderItems.count()
    
    if (folderCount > 0) {
      await folderItems.first().dblclick()
      await page.waitForTimeout(1000)
      
      // 验证错误状态处理
      const errorIndicator = page.locator('.breadcrumb-error')
      if (await errorIndicator.isVisible()) {
        await expect(errorIndicator).toBeVisible()
      } else {
        // 或者验证面包屑回退到安全状态
        const breadcrumbItems = page.locator('.breadcrumb-item')
        const count = await breadcrumbItems.count()
        expect(count).toBeGreaterThanOrEqual(1)
      }
    }
    
    // 清除路由拦截
    await page.unroute('**/api/files/*/breadcrumb*')
  })

  test('should handle breadcrumb copy path functionality', async ({ page }) => {
    // 测试复制路径功能
    const breadcrumbNav = page.locator('.breadcrumb-nav')
    
    // 右键点击面包屑导航
    await breadcrumbNav.click({ button: 'right' })
    await page.waitForTimeout(300)
    
    // 查找复制路径选项
    const copyPathOption = page.locator('.copy-path-option')
    if (await copyPathOption.isVisible()) {
      await copyPathOption.click()
      
      // 验证复制成功提示
      const copySuccess = page.locator('.copy-success-message')
      if (await copySuccess.isVisible()) {
        await expect(copySuccess).toBeVisible()
        await expect(copySuccess).toContainText('路径已复制')
      }
    }
  })
})
