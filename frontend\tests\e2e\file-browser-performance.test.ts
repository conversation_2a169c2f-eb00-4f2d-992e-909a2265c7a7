import { test, expect } from '@playwright/test'

test.describe('FileBrowser Performance Tests', () => {
  let subjectId: number

  test.beforeEach(async ({ page }) => {
    // 确保后端服务器运行
    await page.goto('http://localhost:3001/health')
    await expect(page.locator('body')).toContainText('OK')
    
    // 导航到计算机科学学科（ID=2，已有大量测试文件）
    await page.goto('http://localhost:3000/subjects/2')
    await page.waitForLoadState('networkidle')
    subjectId = 2
    
    // 等待页面加载完成
    await page.waitForSelector('.file-browser', { timeout: 15000 })
  })

  test('should load file list within performance target (<1s)', async ({ page }) => {
    // 测试文件列表加载性能
    const startTime = Date.now()
    
    // 刷新页面触发文件列表加载
    await page.reload()
    await page.waitForSelector('.file-browser-content', { timeout: 10000 })
    
    const endTime = Date.now()
    const loadTime = endTime - startTime
    
    // 验证加载时间 < 1秒
    expect(loadTime).toBeLessThan(1000)
    console.log(`File list load time: ${loadTime}ms`)
    
    // 验证文件列表正确加载
    const fileItems = page.locator('.file-item')
    const count = await fileItems.count()
    expect(count).toBeGreaterThan(0)
  })

  test('should handle search response within target (<500ms)', async ({ page }) => {
    // 测试搜索响应性能
    const searchInput = page.locator('input[placeholder*="搜索文件"]')
    const searchButton = page.locator('.search-button')
    
    // 等待初始加载完成
    await page.waitForSelector('.file-browser-content')
    
    const startTime = Date.now()
    
    // 执行搜索
    await searchInput.fill('测试')
    await searchButton.click()
    
    // 等待搜索结果
    await page.waitForSelector('.search-results, .no-search-results', { timeout: 5000 })
    
    const endTime = Date.now()
    const searchTime = endTime - startTime
    
    // 验证搜索响应时间 < 500ms
    expect(searchTime).toBeLessThan(500)
    console.log(`Search response time: ${searchTime}ms`)
  })

  test('should handle navigation switching within target (<300ms)', async ({ page }) => {
    // 测试导航切换性能
    await page.waitForSelector('.file-browser-content')
    
    const folderItems = page.locator('.file-item.folder')
    const folderCount = await folderItems.count()
    
    if (folderCount > 0) {
      const startTime = Date.now()
      
      // 进入文件夹
      await folderItems.first().dblclick()
      
      // 等待导航完成
      await page.waitForTimeout(500)
      
      const endTime = Date.now()
      const navTime = endTime - startTime
      
      // 验证导航切换时间 < 300ms
      expect(navTime).toBeLessThan(300)
      console.log(`Navigation switch time: ${navTime}ms`)
      
      // 验证面包屑更新
      const breadcrumbItems = page.locator('.breadcrumb-item')
      const count = await breadcrumbItems.count()
      expect(count).toBeGreaterThanOrEqual(2)
    }
  })

  test('should handle virtual scrolling performance with large lists', async ({ page }) => {
    // 测试虚拟滚动性能
    await page.waitForSelector('.file-browser-content')
    
    const fileItems = page.locator('.file-item')
    const totalCount = await fileItems.count()
    
    if (totalCount > 50) {
      const scrollContainer = page.locator('.virtual-scroll-container, .file-list-container')
      
      if (await scrollContainer.isVisible()) {
        const startTime = Date.now()
        
        // 快速滚动到底部
        await scrollContainer.evaluate(el => {
          el.scrollTop = el.scrollHeight
        })
        
        // 等待渲染稳定
        await page.waitForTimeout(200)
        
        // 滚动回顶部
        await scrollContainer.evaluate(el => {
          el.scrollTop = 0
        })
        
        await page.waitForTimeout(200)
        
        const endTime = Date.now()
        const scrollTime = endTime - startTime
        
        // 验证滚动性能 < 1秒
        expect(scrollTime).toBeLessThan(1000)
        console.log(`Virtual scroll time: ${scrollTime}ms`)
        
        // 验证DOM元素数量合理（虚拟滚动应该限制渲染的元素数量）
        const renderedItems = await page.locator('.file-item:visible').count()
        expect(renderedItems).toBeLessThan(totalCount) // 虚拟滚动应该只渲染可见项
      }
    }
  })

  test('should handle view mode switching performance', async ({ page }) => {
    // 测试视图模式切换性能
    await page.waitForSelector('.file-browser-content')
    
    const listViewBtn = page.locator('.view-toggle .list-view')
    const gridViewBtn = page.locator('.view-toggle .grid-view')
    
    if (await listViewBtn.isVisible() && await gridViewBtn.isVisible()) {
      // 切换到网格视图
      const startTime1 = Date.now()
      await gridViewBtn.click()
      await page.waitForSelector('.file-grid', { timeout: 2000 })
      const endTime1 = Date.now()
      const gridSwitchTime = endTime1 - startTime1
      
      // 切换到列表视图
      const startTime2 = Date.now()
      await listViewBtn.click()
      await page.waitForSelector('.file-list', { timeout: 2000 })
      const endTime2 = Date.now()
      const listSwitchTime = endTime2 - startTime2
      
      // 验证视图切换时间 < 500ms
      expect(gridSwitchTime).toBeLessThan(500)
      expect(listSwitchTime).toBeLessThan(500)
      
      console.log(`Grid view switch time: ${gridSwitchTime}ms`)
      console.log(`List view switch time: ${listSwitchTime}ms`)
    }
  })

  test('should handle concurrent operations performance', async ({ page }) => {
    // 测试并发操作性能
    await page.waitForSelector('.file-browser-content')
    
    const startTime = Date.now()
    
    // 同时执行多个操作
    const operations = [
      // 搜索操作
      page.locator('input[placeholder*="搜索文件"]').fill('文档'),
      
      // 类型过滤
      page.locator('.type-filter select').selectOption('file'),
      
      // 视图切换
      page.locator('.view-toggle .grid-view').click()
    ]
    
    // 并发执行所有操作
    await Promise.all(operations)
    
    // 等待所有操作完成
    await page.waitForTimeout(1000)
    
    const endTime = Date.now()
    const concurrentTime = endTime - startTime
    
    // 验证并发操作时间 < 2秒
    expect(concurrentTime).toBeLessThan(2000)
    console.log(`Concurrent operations time: ${concurrentTime}ms`)
  })

  test('should handle memory usage during extended usage', async ({ page }) => {
    // 测试内存使用情况
    await page.waitForSelector('.file-browser-content')
    
    // 获取初始内存使用情况
    const initialMemory = await page.evaluate(() => {
      return (performance as any).memory ? {
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize
      } : null
    })
    
    // 执行一系列操作模拟长时间使用
    for (let i = 0; i < 10; i++) {
      // 搜索操作
      await page.locator('input[placeholder*="搜索文件"]').fill(`测试${i}`)
      await page.locator('.search-button').click()
      await page.waitForTimeout(200)
      
      // 清空搜索
      await page.locator('input[placeholder*="搜索文件"]').clear()
      await page.waitForTimeout(200)
      
      // 视图切换
      if (i % 2 === 0) {
        await page.locator('.view-toggle .grid-view').click()
      } else {
        await page.locator('.view-toggle .list-view').click()
      }
      await page.waitForTimeout(200)
    }
    
    // 获取最终内存使用情况
    const finalMemory = await page.evaluate(() => {
      return (performance as any).memory ? {
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize
      } : null
    })
    
    if (initialMemory && finalMemory) {
      const memoryIncrease = finalMemory.usedJSHeapSize - initialMemory.usedJSHeapSize
      const memoryIncreasePercent = (memoryIncrease / initialMemory.usedJSHeapSize) * 100
      
      console.log(`Memory increase: ${memoryIncrease} bytes (${memoryIncreasePercent.toFixed(2)}%)`)
      
      // 验证内存增长不超过50%
      expect(memoryIncreasePercent).toBeLessThan(50)
    }
  })

  test('should handle large file count rendering performance', async ({ page }) => {
    // 测试大量文件渲染性能
    await page.waitForSelector('.file-browser-content')
    
    const fileItems = page.locator('.file-item')
    const totalCount = await fileItems.count()
    
    console.log(`Total file count: ${totalCount}`)
    
    if (totalCount > 100) {
      const startTime = Date.now()
      
      // 测试页面渲染性能
      await page.evaluate(() => {
        // 强制重新渲染
        document.body.style.display = 'none'
        document.body.offsetHeight // 触发重排
        document.body.style.display = ''
      })
      
      // 等待渲染完成
      await page.waitForTimeout(500)
      
      const endTime = Date.now()
      const renderTime = endTime - startTime
      
      // 验证渲染时间 < 2秒
      expect(renderTime).toBeLessThan(2000)
      console.log(`Large list render time: ${renderTime}ms`)
      
      // 验证所有文件项都正确渲染
      const visibleItems = await page.locator('.file-item:visible').count()
      expect(visibleItems).toBeGreaterThan(0)
    }
  })

  test('should handle network latency gracefully', async ({ page }) => {
    // 测试网络延迟处理
    
    // 模拟慢速网络
    await page.route('**/api/subjects/*/files*', async route => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      route.continue()
    })
    
    const startTime = Date.now()
    
    // 刷新页面
    await page.reload()
    
    // 验证加载状态显示
    const loadingIndicator = page.locator('.loading-spinner, .loading')
    if (await loadingIndicator.isVisible()) {
      await expect(loadingIndicator).toBeVisible()
    }
    
    // 等待加载完成
    await page.waitForSelector('.file-browser-content', { timeout: 10000 })
    
    const endTime = Date.now()
    const totalTime = endTime - startTime
    
    console.log(`Network latency handling time: ${totalTime}ms`)
    
    // 验证在网络延迟情况下仍能正常加载
    const fileItems = page.locator('.file-item')
    const count = await fileItems.count()
    expect(count).toBeGreaterThan(0)
    
    // 清除路由拦截
    await page.unroute('**/api/subjects/*/files*')
  })
})
