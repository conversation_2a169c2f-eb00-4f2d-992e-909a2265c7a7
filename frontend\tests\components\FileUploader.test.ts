import { test, expect } from '@playwright/test'

test.describe('FileUploader Component', () => {
  test.beforeEach(async ({ page }) => {
    // 启动前端服务器并导航到学科详情页面
    await page.goto('http://localhost:3000/subjects/1')
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle')
    
    // 等待FileUploader组件出现
    await page.waitForSelector('.file-uploader', { timeout: 10000 })
  })

  test('should render FileUploader component correctly', async ({ page }) => {
    // 检查上传区域是否存在
    const uploader = page.locator('.file-uploader')
    await expect(uploader).toBeVisible()
    
    // 检查上传拖拽区域
    const dragArea = page.locator('.ant-upload-drag')
    await expect(dragArea).toBeVisible()
    
    // 检查上传图标
    const uploadIcon = page.locator('.upload-icon i')
    await expect(uploadIcon).toBeVisible()
    
    // 检查上传文本
    const uploadText = page.locator('.upload-text')
    await expect(uploadText).toContainText('点击或拖拽 Markdown 文件到此区域上传')
    
    // 检查提示文本
    const uploadHint = page.locator('.upload-hint')
    await expect(uploadHint).toContainText('仅支持 .md 和 .markdown 格式文件')
  })

  test('should handle file selection and validation', async ({ page }) => {
    // 创建一个测试用的Markdown文件
    const testContent = '# 测试文件\n\n这是一个测试Markdown文件。\n\n## 内容\n- 项目1\n- 项目2'
    
    // 模拟文件选择
    const fileInput = page.locator('input[type="file"]')
    
    // 创建一个模拟的Markdown文件
    await fileInput.setInputFiles({
      name: 'test-file.md',
      mimeType: 'text/markdown',
      buffer: Buffer.from(testContent)
    })
    
    // 等待文件处理
    await page.waitForTimeout(1000)
    
    // 检查是否显示上传进度
    const progressBar = page.locator('.ant-progress')
    
    // 等待上传完成或失败状态
    await page.waitForFunction(() => {
      const statusElement = document.querySelector('.upload-content')
      return statusElement && (
        statusElement.textContent?.includes('上传成功') ||
        statusElement.textContent?.includes('上传失败') ||
        statusElement.textContent?.includes('正在上传')
      )
    }, { timeout: 10000 })
  })

  test('should show error for invalid file types', async ({ page }) => {
    // 模拟选择非Markdown文件
    const fileInput = page.locator('input[type="file"]')
    
    await fileInput.setInputFiles({
      name: 'test-file.txt',
      mimeType: 'text/plain',
      buffer: Buffer.from('This is a text file')
    })
    
    // 等待错误消息出现
    await page.waitForSelector('.upload-error', { timeout: 5000 })
    
    // 检查错误消息
    const errorAlert = page.locator('.upload-error .ant-alert-message')
    await expect(errorAlert).toContainText('只能上传 Markdown 文件')
  })

  test('should show error for oversized files', async ({ page }) => {
    // 创建一个超大的文件内容（模拟超过10MB）
    const largeContent = 'x'.repeat(11 * 1024 * 1024) // 11MB
    
    const fileInput = page.locator('input[type="file"]')
    
    await fileInput.setInputFiles({
      name: 'large-file.md',
      mimeType: 'text/markdown',
      buffer: Buffer.from(largeContent)
    })
    
    // 等待错误消息出现
    await page.waitForSelector('.upload-error', { timeout: 5000 })
    
    // 检查错误消息
    const errorAlert = page.locator('.upload-error .ant-alert-message')
    await expect(errorAlert).toContainText('文件大小不能超过')
  })

  test('should handle upload progress display', async ({ page }) => {
    // 创建测试文件
    const testContent = '# 测试进度\n\n测试上传进度显示功能。'
    
    const fileInput = page.locator('input[type="file"]')
    
    await fileInput.setInputFiles({
      name: 'progress-test.md',
      mimeType: 'text/markdown',
      buffer: Buffer.from(testContent)
    })
    
    // 检查是否显示上传进度
    try {
      await page.waitForSelector('.upload-progress', { timeout: 3000 })
      
      const progressBar = page.locator('.ant-progress')
      await expect(progressBar).toBeVisible()
      
      const progressText = page.locator('.progress-text')
      await expect(progressText).toContainText('正在上传中')
    } catch (error) {
      // 如果上传太快，可能看不到进度条，这是正常的
      console.log('Upload completed too quickly to see progress bar')
    }
  })

  test('should be responsive on different screen sizes', async ({ page }) => {
    // 测试桌面端
    await page.setViewportSize({ width: 1200, height: 800 })
    const uploader = page.locator('.file-uploader')
    await expect(uploader).toBeVisible()
    
    // 测试平板端
    await page.setViewportSize({ width: 768, height: 1024 })
    await expect(uploader).toBeVisible()
    
    // 测试移动端
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(uploader).toBeVisible()
    
    // 检查上传区域在移动端仍然可用
    const dragArea = page.locator('.ant-upload-drag')
    await expect(dragArea).toBeVisible()
  })

  test('should handle component disabled state', async ({ page }) => {
    // 这个测试需要修改组件props，暂时跳过
    // 在实际应用中，可以通过路由参数或其他方式来测试disabled状态
    test.skip()
  })

  test('should clear error state when clicking close button', async ({ page }) => {
    // 首先触发一个错误
    const fileInput = page.locator('input[type="file"]')
    
    await fileInput.setInputFiles({
      name: 'invalid-file.txt',
      mimeType: 'text/plain',
      buffer: Buffer.from('Invalid file')
    })
    
    // 等待错误消息出现
    await page.waitForSelector('.upload-error', { timeout: 5000 })
    
    // 点击关闭按钮
    const closeButton = page.locator('.upload-error .ant-alert-close-icon')
    await closeButton.click()
    
    // 检查错误消息是否消失
    await expect(page.locator('.upload-error')).not.toBeVisible()
  })
})
