#!/usr/bin/env node

/**
 * 文件浏览功能端到端测试运行脚本
 * 专门用于运行文件浏览相关的Playwright测试
 */

import { spawn } from 'child_process'
import path from 'path'
import fs from 'fs'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 测试配置
const TEST_CONFIG = {
  // 测试文件列表
  testFiles: [
    'file-browser.test.ts',
    'file-search.test.ts',
    'breadcrumb-nav.test.ts',
    'file-browser-performance.test.ts'
  ],

  // 浏览器配置
  browsers: ['chromium', 'firefox', 'webkit'],

  // 测试环境配置
  environment: {
    NODE_ENV: 'test',
    VITE_API_BASE_URL: 'http://localhost:3001'
  },

  // 超时配置
  timeout: {
    test: 60000,      // 单个测试60秒
    expect: 10000,    // 断言10秒
    navigation: 30000 // 导航30秒
  }
}

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 检查前置条件
async function checkPrerequisites() {
  colorLog('blue', '🔍 检查测试前置条件...')

  // 检查测试文件是否存在
  const testDir = __dirname
  const missingFiles = []

  for (const testFile of TEST_CONFIG.testFiles) {
    const filePath = path.join(testDir, testFile)
    if (!fs.existsSync(filePath)) {
      missingFiles.push(testFile)
    }
  }

  if (missingFiles.length > 0) {
    colorLog('red', `❌ 缺少测试文件: ${missingFiles.join(', ')}`)
    return false
  }

  // 检查Playwright配置文件
  const configPath = path.join(testDir, 'playwright.e2e.config.ts')
  if (!fs.existsSync(configPath)) {
    colorLog('red', '❌ 缺少Playwright配置文件: playwright.e2e.config.ts')
    return false
  }

  colorLog('green', '✅ 前置条件检查通过')
  return true
}

// 检查服务器状态
async function checkServers() {
  colorLog('blue', '🌐 检查服务器状态...')

  try {
    // 检查前端服务器
    const frontendResponse = await fetch('http://localhost:3000')
    if (!frontendResponse.ok) {
      throw new Error('前端服务器未响应')
    }

    // 检查后端服务器
    const backendResponse = await fetch('http://localhost:3001/health')
    if (!backendResponse.ok) {
      throw new Error('后端服务器未响应')
    }

    colorLog('green', '✅ 服务器状态正常')
    return true
  } catch (error) {
    colorLog('red', `❌ 服务器检查失败: ${error.message}`)
    colorLog('yellow', '💡 请确保前端服务器(3000)和后端服务器(3001)正在运行')
    return false
  }
}

// 运行单个测试文件
function runTestFile(testFile, browser = 'chromium') {
  return new Promise((resolve, reject) => {
    colorLog('cyan', `🧪 运行测试: ${testFile} (${browser})`)

    const args = [
      'test',
      testFile,
      '--config=playwright.e2e.config.ts',
      `--project=${browser}`,
      '--reporter=list'
    ]

    const child = spawn('npx', ['playwright', ...args], {
      cwd: __dirname,
      env: { ...process.env, ...TEST_CONFIG.environment },
      stdio: 'pipe'
    })

    let output = ''
    let errorOutput = ''

    child.stdout.on('data', (data) => {
      const text = data.toString()
      output += text
      process.stdout.write(text)
    })

    child.stderr.on('data', (data) => {
      const text = data.toString()
      errorOutput += text
      process.stderr.write(text)
    })

    child.on('close', (code) => {
      if (code === 0) {
        colorLog('green', `✅ ${testFile} (${browser}) 测试通过`)
        resolve({ testFile, browser, success: true, output })
      } else {
        colorLog('red', `❌ ${testFile} (${browser}) 测试失败 (退出码: ${code})`)
        reject({ testFile, browser, success: false, code, output, errorOutput })
      }
    })

    child.on('error', (error) => {
      colorLog('red', `❌ ${testFile} (${browser}) 执行错误: ${error.message}`)
      reject({ testFile, browser, success: false, error: error.message })
    })
  })
}

// 运行所有测试
async function runAllTests() {
  const results = []
  const startTime = Date.now()

  colorLog('bright', '🚀 开始运行文件浏览功能测试套件')
  colorLog('blue', `📋 测试文件: ${TEST_CONFIG.testFiles.length} 个`)
  colorLog('blue', `🌐 浏览器: ${TEST_CONFIG.browsers.join(', ')}`)

  for (const testFile of TEST_CONFIG.testFiles) {
    for (const browser of TEST_CONFIG.browsers) {
      try {
        const result = await runTestFile(testFile, browser)
        results.push(result)
      } catch (error) {
        results.push(error)
      }
    }
  }

  const endTime = Date.now()
  const duration = Math.round((endTime - startTime) / 1000)

  // 统计结果
  const passed = results.filter(r => r.success).length
  const failed = results.filter(r => !r.success).length
  const total = results.length

  colorLog('bright', '\n📊 测试结果统计:')
  colorLog('green', `✅ 通过: ${passed}`)
  colorLog('red', `❌ 失败: ${failed}`)
  colorLog('blue', `📈 总计: ${total}`)
  colorLog('yellow', `⏱️  耗时: ${duration}秒`)

  if (failed > 0) {
    colorLog('red', '\n❌ 失败的测试:')
    results.filter(r => !r.success).forEach(result => {
      colorLog('red', `  - ${result.testFile} (${result.browser})`)
    })
  }

  return { passed, failed, total, duration, results }
}

// 生成测试报告
function generateReport(testResults) {
  const reportPath = path.join(__dirname, 'test-results', 'file-browser-test-report.json')
  const reportDir = path.dirname(reportPath)

  // 确保报告目录存在
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true })
  }

  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      passed: testResults.passed,
      failed: testResults.failed,
      total: testResults.total,
      duration: testResults.duration,
      successRate: Math.round((testResults.passed / testResults.total) * 100)
    },
    environment: TEST_CONFIG.environment,
    browsers: TEST_CONFIG.browsers,
    testFiles: TEST_CONFIG.testFiles,
    results: testResults.results
  }

  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  colorLog('blue', `📄 测试报告已生成: ${reportPath}`)

  return reportPath
}

// 主函数
async function main() {
  try {
    colorLog('bright', '🎯 文件浏览功能端到端测试')
    colorLog('blue', '='.repeat(50))

    // 检查前置条件
    if (!(await checkPrerequisites())) {
      process.exit(1)
    }

    // 检查服务器状态
    if (!(await checkServers())) {
      process.exit(1)
    }

    // 运行测试
    const testResults = await runAllTests()

    // 生成报告
    generateReport(testResults)

    // 根据测试结果设置退出码
    if (testResults.failed > 0) {
      colorLog('red', '\n❌ 部分测试失败')
      process.exit(1)
    } else {
      colorLog('green', '\n🎉 所有测试通过!')
      process.exit(0)
    }

  } catch (error) {
    colorLog('red', `💥 测试执行出错: ${error.message}`)
    console.error(error)
    process.exit(1)
  }
}

// 处理命令行参数
const args = process.argv.slice(2)
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
文件浏览功能端到端测试运行器

用法:
  node run-file-browser-tests.js [选项]

选项:
  --help, -h     显示帮助信息
  --single       只运行单个浏览器(chromium)
  --fast         快速模式，跳过性能测试

测试文件:
  ${TEST_CONFIG.testFiles.map(f => `  - ${f}`).join('\n')}

浏览器:
  ${TEST_CONFIG.browsers.map(b => `  - ${b}`).join('\n')}
`)
  process.exit(0)
}

// 处理特殊参数
if (args.includes('--single')) {
  TEST_CONFIG.browsers = ['chromium']
  colorLog('yellow', '⚡ 单浏览器模式: 只运行 Chromium')
}

if (args.includes('--fast')) {
  TEST_CONFIG.testFiles = TEST_CONFIG.testFiles.filter(f => !f.includes('performance'))
  colorLog('yellow', '🏃 快速模式: 跳过性能测试')
}

// 运行主函数
main().catch(error => {
  colorLog('red', `💥 未处理的错误: ${error.message}`)
  console.error(error)
  process.exit(1)
})
