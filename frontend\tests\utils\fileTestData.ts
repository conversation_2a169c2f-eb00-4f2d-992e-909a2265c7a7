/**
 * 文件测试数据生成工具
 * 用于为Playwright测试生成模拟文件数据
 */

export interface MockFileNode {
  id: number
  subject_id: number
  parent_id: number | null
  name: string
  type: 'file' | 'folder'
  file_path: string | null
  file_size: number | null
  mime_type: string | null
  created_at: string
  updated_at: string
}

export interface MockFileListResponse {
  success: boolean
  message: string
  data: {
    subjectId: number
    parentId: number | null
    files: MockFileNode[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
      hasNextPage: boolean
      hasPrevPage: boolean
    }
    filters: {
      search: string | null
      type: string | null
    }
  }
  timestamp: string
  responseTime: string
  requestId: string
}

export class FileTestDataGenerator {
  private static fileExtensions = {
    document: ['pdf', 'doc', 'docx', 'txt', 'md', 'rtf'],
    image: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'],
    video: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'],
    audio: ['mp3', 'wav', 'flac', 'aac', 'ogg'],
    archive: ['zip', 'rar', '7z', 'tar', 'gz'],
    code: ['js', 'ts', 'py', 'java', 'cpp', 'html', 'css']
  }

  private static folderNames = [
    '第一章', '第二章', '第三章', '第四章', '第五章',
    '作业', '练习', '实验', '项目', '资料',
    '笔记', '课件', '参考资料', '习题解答', '复习资料',
    '期中考试', '期末考试', '模拟试题', '真题', '答案'
  ]

  private static fileNamePrefixes = [
    '计算机基础', '数据结构', '算法分析', '操作系统', '数据库',
    '网络编程', '软件工程', '人工智能', '机器学习', '深度学习',
    '前端开发', '后端开发', '移动开发', '游戏开发', '系统设计'
  ]

  /**
   * 生成单个文件节点
   */
  static generateFileNode(
    id: number,
    subjectId: number,
    parentId: number | null = null,
    type: 'file' | 'folder' = 'file'
  ): MockFileNode {
    const now = new Date().toISOString().replace('T', ' ').substring(0, 19)
    
    if (type === 'folder') {
      const folderName = this.folderNames[Math.floor(Math.random() * this.folderNames.length)]
      const suffix = Math.floor(Math.random() * 1000)
      
      return {
        id,
        subject_id: subjectId,
        parent_id: parentId,
        name: `${folderName}_${suffix}`,
        type: 'folder',
        file_path: `/uploads/subject_${subjectId}/${folderName}_${suffix}`,
        file_size: null,
        mime_type: null,
        created_at: now,
        updated_at: now
      }
    } else {
      const categoryKeys = Object.keys(this.fileExtensions)
      const category = categoryKeys[Math.floor(Math.random() * categoryKeys.length)]
      const extensions = this.fileExtensions[category as keyof typeof this.fileExtensions]
      const extension = extensions[Math.floor(Math.random() * extensions.length)]
      
      const prefix = this.fileNamePrefixes[Math.floor(Math.random() * this.fileNamePrefixes.length)]
      const suffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
      const fileName = `${prefix}_${suffix}.${extension}`
      
      return {
        id,
        subject_id: subjectId,
        parent_id: parentId,
        name: fileName,
        type: 'file',
        file_path: `/uploads/subject_${subjectId}/${fileName}`,
        file_size: Math.floor(Math.random() * 10000000) + 1000, // 1KB - 10MB
        mime_type: this.getMimeType(extension),
        created_at: now,
        updated_at: now
      }
    }
  }

  /**
   * 生成文件列表响应数据
   */
  static generateFileListResponse(
    subjectId: number,
    parentId: number | null = null,
    count: number = 50,
    page: number = 1,
    limit: number = 50
  ): MockFileListResponse {
    const files: MockFileNode[] = []
    let currentId = 1

    // 生成文件夹（30%的概率）
    const folderCount = Math.floor(count * 0.3)
    for (let i = 0; i < folderCount; i++) {
      files.push(this.generateFileNode(currentId++, subjectId, parentId, 'folder'))
    }

    // 生成文件（70%的概率）
    const fileCount = count - folderCount
    for (let i = 0; i < fileCount; i++) {
      files.push(this.generateFileNode(currentId++, subjectId, parentId, 'file'))
    }

    // 按名称排序
    files.sort((a, b) => a.name.localeCompare(b.name))

    const totalPages = Math.ceil(count / limit)
    const startIndex = (page - 1) * limit
    const endIndex = Math.min(startIndex + limit, count)
    const paginatedFiles = files.slice(startIndex, endIndex)

    return {
      success: true,
      message: '获取文件列表成功',
      data: {
        subjectId,
        parentId,
        files: paginatedFiles,
        pagination: {
          page,
          limit,
          total: count,
          totalPages,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        },
        filters: {
          search: null,
          type: null
        }
      },
      timestamp: new Date().toISOString(),
      responseTime: `${Math.floor(Math.random() * 50) + 5}ms`,
      requestId: `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }
  }

  /**
   * 生成搜索结果响应数据
   */
  static generateSearchResponse(
    subjectId: number,
    query: string,
    count: number = 20
  ): any {
    const files: MockFileNode[] = []
    let currentId = 1000

    for (let i = 0; i < count; i++) {
      const file = this.generateFileNode(currentId++, subjectId, null, 'file')
      // 确保文件名包含搜索关键词
      file.name = `${query}_${file.name}`
      files.push(file)
    }

    return {
      success: true,
      message: '搜索完成',
      data: {
        subjectId,
        query,
        results: files,
        total: count,
        searchTime: `${Math.floor(Math.random() * 200) + 50}ms`
      },
      timestamp: new Date().toISOString(),
      responseTime: `${Math.floor(Math.random() * 300) + 100}ms`,
      requestId: `search-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }
  }

  /**
   * 生成面包屑导航响应数据
   */
  static generateBreadcrumbResponse(
    fileId: number,
    subjectId: number,
    depth: number = 3
  ): any {
    const breadcrumb = []

    // 添加学科根节点
    breadcrumb.push({
      id: null,
      name: '测试学科',
      type: 'subject',
      path: '/'
    })

    // 添加中间文件夹节点
    for (let i = 1; i < depth; i++) {
      breadcrumb.push({
        id: i * 100,
        name: `文件夹_${i}`,
        type: 'folder',
        path: `/folder_${i}`
      })
    }

    // 添加当前节点
    breadcrumb.push({
      id: fileId,
      name: `当前文件夹_${fileId}`,
      type: 'folder',
      path: `/current_${fileId}`
    })

    return {
      success: true,
      message: '获取面包屑导航成功',
      data: {
        breadcrumb
      },
      timestamp: new Date().toISOString(),
      responseTime: `${Math.floor(Math.random() * 100) + 10}ms`,
      requestId: `breadcrumb-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }
  }

  /**
   * 根据文件扩展名获取MIME类型
   */
  private static getMimeType(extension: string): string {
    const mimeTypes: { [key: string]: string } = {
      // 文档
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'txt': 'text/plain',
      'md': 'text/markdown',
      'rtf': 'application/rtf',
      
      // 图片
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'bmp': 'image/bmp',
      'svg': 'image/svg+xml',
      
      // 视频
      'mp4': 'video/mp4',
      'avi': 'video/x-msvideo',
      'mov': 'video/quicktime',
      'wmv': 'video/x-ms-wmv',
      'flv': 'video/x-flv',
      'mkv': 'video/x-matroska',
      
      // 音频
      'mp3': 'audio/mpeg',
      'wav': 'audio/wav',
      'flac': 'audio/flac',
      'aac': 'audio/aac',
      'ogg': 'audio/ogg',
      
      // 压缩包
      'zip': 'application/zip',
      'rar': 'application/x-rar-compressed',
      '7z': 'application/x-7z-compressed',
      'tar': 'application/x-tar',
      'gz': 'application/gzip',
      
      // 代码
      'js': 'application/javascript',
      'ts': 'application/typescript',
      'py': 'text/x-python',
      'java': 'text/x-java-source',
      'cpp': 'text/x-c++src',
      'html': 'text/html',
      'css': 'text/css'
    }

    return mimeTypes[extension] || 'application/octet-stream'
  }

  /**
   * 生成大量测试数据用于性能测试
   */
  static generateLargeDataset(
    subjectId: number,
    totalFiles: number = 1000
  ): MockFileNode[] {
    const files: MockFileNode[] = []
    
    for (let i = 1; i <= totalFiles; i++) {
      const type = Math.random() < 0.2 ? 'folder' : 'file' // 20%文件夹，80%文件
      files.push(this.generateFileNode(i, subjectId, null, type))
    }
    
    return files.sort((a, b) => a.name.localeCompare(b.name))
  }
}
